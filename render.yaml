services:
  - type: web
    name: video-to-audio-api
    env: python
    plan: free
    buildCommand: pip install -r requirements.txt
    startCommand: gunicorn --bind 0.0.0.0:$PORT --workers 1 --timeout 300 --max-requests 100 --max-requests-jitter 10 app:app
    envVars:
      - key: FLASK_ENV
        value: production
      - key: LOG_LEVEL
        value: INFO
      - key: PYTHONUNBUFFERED
        value: 1
    healthCheckPath: /health
    autoDeploy: true
